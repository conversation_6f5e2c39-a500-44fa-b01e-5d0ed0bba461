<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('temporary_media_uploads', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->index();
            $table->uuid('uuid')->unique();
            $table->string('name')->nullable();
            $table->string('collection_name')->default('default');
            $table->string('original_name')->nullable();
            $table->bigInteger('file_size')->nullable();
            $table->string('mime_type')->nullable();
            $table->timestamp('expires_at')->nullable()->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('temporary_media_uploads');
    }
};
