<!DOCTYPE html>
<html>
<head>
    <title>Test Upload</title>
</head>
<body>
    <h1>Test RajaPicker Upload</h1>

    <form id="uploadForm" enctype="multipart/form-data">
        <input type="file" id="fileInput" accept="image/*" required>
        <select id="collectionSelect">
            <option value="default">Default</option>
            <option value="cms">CMS</option>
            <option value="gallery">Gallery</option>
        </select>
        <button type="submit">Upload</button>
    </form>

    <div id="result"></div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const fileInput = document.getElementById('fileInput');
            const collection = document.getElementById('collectionSelect').value;
            const resultDiv = document.getElementById('result');

            if (!fileInput.files[0]) {
                alert('Please select a file');
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('collection', collection);

            try {
                resultDiv.innerHTML = 'Uploading...';

                const response = await fetch('/api/media/upload', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                });

                const responseText = await response.text();
                console.log('Response:', responseText);

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    result = { error: 'Invalid JSON response', response: responseText };
                }

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <h3>Upload Success!</h3>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>Upload Failed! Status: ${response.status}</h3>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Error!</h3>
                    <pre>${error.message}</pre>
                `;
            }
        });
    </script>
</body>
</html>
