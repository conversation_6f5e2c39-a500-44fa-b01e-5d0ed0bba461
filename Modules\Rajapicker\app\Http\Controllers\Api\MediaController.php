<?php

namespace Modules\Rajapicker\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Modules\Rajapicker\Models\Media;
use Modules\Rajapicker\Models\RajaGaleri;
use Modules\Rajapicker\Models\TemporaryMediaUpload;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Modules\Rajapicker\Services\WebPConverterService;
use Modules\Rajapicker\Services\RajaPickerThumbnailService;
use Modules\Rajapicker\Services\RajaPickerConfigService;

class MediaController extends Controller
{
    protected RajaPickerThumbnailService $thumbnailService;

    public function __construct(RajaPickerThumbnailService $thumbnailService)
    {
        $this->thumbnailService = $thumbnailService;
    }

    /**
     * Get media by ID with detailed information
     */
    public function show($id): JsonResponse
    {
        try {
            $media = Media::find($id);

            if (!$media) {
                return response()->json(['error' => 'Media tidak ditemukan'], 404);
            }

            // Get image dimensions if it's an image
            $dimensions = null;
            if ($media->isImage()) {
                try {
                    // Try multiple possible paths
                    $possiblePaths = [
                        storage_path('app/public/uploads/' . $media->collection_name . '/' . $media->file_name),
                        storage_path('app/public/' . $media->collection_name . '/' . $media->file_name),
                        public_path('storage/uploads/' . $media->collection_name . '/' . $media->file_name),
                    ];

                    foreach ($possiblePaths as $fullPath) {
                        if (file_exists($fullPath)) {
                            $imageInfo = getimagesize($fullPath);
                            if ($imageInfo) {
                                $dimensions = [
                                    'width' => $imageInfo[0],
                                    'height' => $imageInfo[1]
                                ];
                                break;
                            }
                        }
                    }
                } catch (\Exception $e) {
                    Log::warning('Could not get image dimensions for media ID: ' . $media->id . ' - ' . $e->getMessage());
                }
            }

            // Get original file URL (not thumbnail)
            $originalUrl = asset('storage/uploads/' . $media->collection_name . '/' . $media->file_name);

            // Get thumbnail URL
            $thumbnailUrl = null;
            if ($media->isImage()) {
                $configService = new \Modules\Rajapicker\Services\RajaPickerConfigService();
                $imagePath = 'uploads/' . $media->collection_name . '/' . $media->file_name;
                $defaultPreviewSize = $configService->getThumbnailConfig()['default_preview_size'] ?? 'th';
                $thumbnailUrl = $this->thumbnailService->getThumbnailUrl($imagePath, $defaultPreviewSize);

                if ($thumbnailUrl && !str_starts_with($thumbnailUrl, 'http')) {
                    $thumbnailUrl = config('app.url') . $thumbnailUrl;
                }
            }

            return response()->json([
                'id' => $media->id,
                'name' => $media->name,
                'file_name' => $media->file_name,
                'original_url' => $originalUrl,
                'thumbnail_url' => $thumbnailUrl,
                'mime_type' => $media->mime_type,
                'size' => $media->size,
                'size_human' => $this->formatBytes($media->size),
                'dimensions' => $dimensions,
                'collection_name' => $media->collection_name,
                'created_at' => $media->created_at,
                'updated_at' => $media->updated_at,
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting media by ID: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan'], 500);
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2): string
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Get multiple media by IDs
     */
    public function getByIds(Request $request): JsonResponse
    {
        try {
            $ids = explode(',', $request->get('ids', ''));
            $ids = array_filter($ids, 'is_numeric');

            if (empty($ids)) {
                return response()->json([]);
            }

            $media = Media::whereIn('id', $ids)->get();

            $result = $media->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'file_name' => $item->file_name,
                    'url' => $this->getRelativeUrl($item),
                    'mime_type' => $item->mime_type,
                    'size' => $item->size,
                    'collection_name' => $item->collection_name,
                    'created_at' => $item->created_at,
                ];
            });

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Error getting media by IDs: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan'], 500);
        }
    }

    /**
     * Get media by collection
     */
    public function getByCollection($collection): JsonResponse
    {
        try {
            $media = Media::where('collection_name', $collection)
                ->where('mime_type', 'LIKE', 'image/%')
                ->orderBy('created_at', 'desc')
                ->limit(100) // Batasi untuk performa
                ->get();

            $result = $media->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'file_name' => $item->file_name,
                    'url' => $this->getRelativeUrl($item),
                    'mime_type' => $item->mime_type,
                    'size' => $item->size,
                    'collection_name' => $item->collection_name,
                    'created_at' => $item->created_at,
                ];
            });

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Error getting media by collection: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan'], 500);
        }
    }

    /**
     * Get all images from all collections (excluding conversion directory) with pagination
     */
    public function getAllImages(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 24); // Default 24 items per page
            $collection = $request->get('collection', null); // Filter by collection
            $byUser = $request->get('by_user', false); // Filter by current user
            $userId = $request->get('user_id', null); // Filter by specific user ID

            $query = Media::where('mime_type', 'LIKE', 'image/%')
                ->where(function ($query) {
                    // Exclude conversion directory files
                    $query->where('collection_name', '!=', 'conversion')
                          ->whereNotLike('file_name', '%/conversion/%');
                });

            // Filter by collection if specified
            if ($collection && $collection !== 'all') {
                $query->where('collection_name', $collection);
            }

            // Filter by specific user ID if specified
            if ($userId !== null) {
                $query->where('user_id', $userId);
            }
            // Filter by current user if byUser is enabled and userId is not specified
            elseif ($byUser && Auth::check()) {
                $query->where('user_id', Auth::id());
            }

            $media = $query->orderBy('created_at', 'desc')
                ->paginate($perPage);

            $result = $media->getCollection()->map(function ($item) {
                // Ambil dimensi dari custom_properties saja (tanpa akses file fisik)
                $dimensions = [];
                if (isset($item->custom_properties['width']) && isset($item->custom_properties['height'])) {
                    $dimensions = [
                        'width' => $item->custom_properties['width'],
                        'height' => $item->custom_properties['height'],
                    ];
                }

                // Dapatkan thumbnail URL dari service custom
                $filePath = $item->getRelativeUrlAttribute();
                $previewSize = (new \Modules\Rajapicker\Services\RajaPickerConfigService())->getDefaultPreviewThumbnailSize();
                $thumbnailUrl = $this->thumbnailService->getThumbnailUrl($filePath, $previewSize);
                
                if ($filePath) {
                    $thumbnailUrl = $this->thumbnailService->getThumbnailUrl($filePath, $previewSize);
                }

                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'file_name' => $item->file_name,
                    'url' => $this->getRelativeUrl($item),
                    'thumbnail_url' => $thumbnailUrl,
                    'mime_type' => $item->mime_type,
                    'size' => $item->size,
                    'collection_name' => $item->collection_name,
                    'width' => $dimensions['width'] ?? null,
                    'height' => $dimensions['height'] ?? null,
                    'created_at' => $item->created_at,
                ];
            });

            return response()->json([
                'data' => $result,
                'pagination' => [
                    'current_page' => $media->currentPage(),
                    'last_page' => $media->lastPage(),
                    'per_page' => $media->perPage(),
                    'total' => $media->total(),
                    'from' => $media->firstItem(),
                    'to' => $media->lastItem(),
                    'has_more_pages' => $media->hasMorePages(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting all images: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan'], 500);
        }
    }

    /**
     * Get all available collections
     */
    public function getCollections(Request $request): JsonResponse
    {
        try {
            $byUser = $request->get('by_user', false); // Filter by current user
            $userId = $request->get('user_id', null); // Filter by specific user ID

            $query = Media::where('mime_type', 'LIKE', 'image/%')
                ->where('collection_name', '!=', 'conversion')
                ->whereNotLike('file_name', '%/conversion/%');

            // Filter by specific user ID if specified
            if ($userId !== null) {
                $query->where('user_id', $userId);
            }
            // Filter by current user if byUser is enabled and userId is not specified
            elseif ($byUser && Auth::check()) {
                $query->where('user_id', Auth::id());
            }

            $collections = $query->distinct()
                ->pluck('collection_name')
                ->filter()
                ->sort()
                ->values();

            return response()->json($collections);
        } catch (\Exception $e) {
            Log::error('Error getting collections: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan'], 500);
        }
    }

    /**
     * Get image dimensions from media item
     */
    private function getImageDimensions($media): array
    {
        try {
            // Hanya cek di custom_properties untuk performa, tanpa membaca file fisik
            if (isset($media->custom_properties['width']) && isset($media->custom_properties['height'])) {
                return [
                    'width'  => $media->custom_properties['width'],
                    'height' => $media->custom_properties['height'],
                ];
            }

            return [];
        } catch (\Exception $e) {
            Log::warning('Could not get image dimensions for media ID: ' . $media->id);
            return [];
        }
    }

    /**
     * Upload media files
     */
    public function upload(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'file' => 'required_without:files|file|mimes:jpeg,png,gif,webp|max:10240',
                'files.*' => 'required_without:files|file|mimes:jpeg,png,gif,webp|max:10240',
                'collection' => 'required|string|max:255',
                'convertWebp' => 'nullable|string|in:true,false',
            ]);

            $collection = $request->input('collection', 'default');
            // Tentukan apakah konversi WebP diaktifkan
            if ($request->has('convertWebp')) {
                $convertWebp = $request->input('convertWebp') === 'true';
            } else {
                // fallback ke konfigurasi global
                $convertWebp = config('rajapicker.storage.webp.default', false);
            }
            $uploadedMedia = [];

            // Handle single file upload
            if ($request->hasFile('file')) {
                $media = $this->processFileUpload($request->file('file'), $collection, $convertWebp);
                if ($media) {
                    $uploadedMedia[] = $media;
                }
            }

            // Handle multiple files upload
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    $media = $this->processFileUpload($file, $collection, $convertWebp);
                    if ($media) {
                        $uploadedMedia[] = $media;
                    }
                }
            }

            if (empty($uploadedMedia)) {
                return response()->json(['error' => 'Tidak ada file yang berhasil diupload'], 400);
            }

            // Return single media or array based on upload type
            if ($request->hasFile('file') && count($uploadedMedia) === 1) {
                return response()->json([
                    'success' => true,
                    'message' => 'File berhasil diupload',
                    'media' => $uploadedMedia[0]
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'message' => count($uploadedMedia) . ' file berhasil diupload',
                    'media' => $uploadedMedia
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Error uploading media: ' . $e->getMessage());
            return response()->json(['error' => 'Gagal mengupload file: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Process single file upload using temporary upload pattern
     */
    private function processFileUpload($file, $collection, $convertWebp = false)
    {
        try {
            // Step 1: Create temporary upload instance
            $temporaryUpload = new TemporaryMediaUpload();
            $temporaryUpload->collection_name = $collection;
            $temporaryUpload->original_name = $file->getClientOriginalName();
            $temporaryUpload->file_size = $file->getSize();
            $temporaryUpload->mime_type = $file->getMimeType();
            $temporaryUpload->save();

            // Step 2: Handle WebP conversion if enabled
            $processedFile = $file;
            if ($convertWebp && $this->isImageFile($file)) {
                $webpConverter = new WebPConverterService();

                // Cek apakah WebP didukung
                if ($webpConverter->isWebPSupported()) {
                    // Konversi ke WebP menggunakan method khusus untuk file upload
                    $webpResult = $webpConverter->convertUploadedFileToWebP($file, [
                        'quality' => 80
                    ]);

                    if ($webpResult && $webpResult['success']) {
                        // Gunakan file WebP yang sudah dikonversi
                        $webpPath = $webpResult['webp_path'];

                        // Buat file upload dari WebP
                        $webpFile = new \Illuminate\Http\UploadedFile(
                            $webpPath,
                            $webpResult['webp_filename'],
                            'image/webp',
                            null,
                            true
                        );

                        // Gunakan file WebP untuk upload
                        $processedFile = $webpFile;

                        Log::info('WebP conversion successful', [
                            'original' => $webpResult['original_path'],
                            'webp' => $webpPath,
                            'compression_ratio' => $webpResult['compression_ratio'],
                            'original_size' => $webpResult['original_size'],
                            'webp_size' => $webpResult['webp_size']
                        ]);
                    } else {
                        Log::warning('WebP conversion failed, using original file', [
                            'error' => $webpResult['error'] ?? 'Unknown error'
                        ]);
                    }
                } else {
                    Log::warning('WebP not supported on this server, using original file');
                }
            }

            // Step 3: Save file to temporary directory using Spatie Media Library
            $tempDir = storage_path('app' . DIRECTORY_SEPARATOR . config('media-library.temporary_directory_path', 'livewire-tmp'));

            // Ensure directory exists and is writable
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            // Try to make it writable if it's not
            if (!is_writable($tempDir)) {
                chmod($tempDir, 0755);
            }

            Log::info('Temporary directory path', [
                'tempDir' => $tempDir,
                'exists' => file_exists($tempDir),
                'writable' => is_writable($tempDir),
                'permissions' => substr(sprintf('%o', fileperms($tempDir)), -4)
            ]);

            // Generate unique filename for temporary storage
            $tempFileName = Str::random(40) . '.' . $processedFile->getClientOriginalExtension();
            $tempPath = $tempDir . DIRECTORY_SEPARATOR . $tempFileName;

            Log::info('About to move file', ['from' => $processedFile->getPathname(), 'to' => $tempPath]);

            // Move file to temporary directory
            $moveResult = $processedFile->move($tempDir, $tempFileName);

            Log::info('Move result', ['result' => $moveResult, 'file_exists' => file_exists($tempPath)]);

            if (!$moveResult) {
                throw new \Exception('Failed to move file to temporary directory');
            }

            // Step 4: Add media to temporary upload model
            $media = $temporaryUpload
                ->addMedia($tempPath)
                ->usingName($processedFile->getClientOriginalName())
                ->usingFileName($this->generateFileName($processedFile))
                ->toMediaCollection('default');

            // Set user_id secara manual karena Spatie Media Library tidak menggunakan model events
            if (Auth::check()) {
                $media->update(['user_id' => Auth::id()]);
                // Refresh model untuk mendapatkan data terbaru
                $media->refresh();
            }

            // Generate thumbnail menggunakan service custom
            $this->generateThumbnailsForMedia($media);

            // Return temporary upload data instead of final media data
            return [
                'temporary_upload_id' => $temporaryUpload->uuid,
                'temp_model_id' => $temporaryUpload->id,
                'is_temporary' => true,
                'id' => $media->id,
                'name' => $media->name,
                'file_name' => $media->file_name,
                'url' => $this->getRelativeUrl($media),
                'thumbnail_url' => $this->thumbnailService->getThumbnailUrl($media->getRelativeUrlAttribute(), (new \Modules\Rajapicker\Services\RajaPickerConfigService())->getDefaultPreviewThumbnailSize()),
                'mime_type' => $media->mime_type,
                'size' => $media->size,
                'collection_name' => $collection,
                'original_name' => $temporaryUpload->original_name,
                'user_id' => $media->user_id,
                'created_at' => $media->created_at,
                'expires_at' => $temporaryUpload->expires_at,
            ];

        } catch (\Exception $e) {
            Log::error('Error processing temporary file upload: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Move temporary upload to final destination
     */
    public function moveTemporaryToFinal(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'temporary_upload_id' => 'required|string',
                'collection' => 'required|string|max:255',
            ]);

            $temporaryUpload = TemporaryMediaUpload::where('uuid', $request->temporary_upload_id)
                ->forCurrentSession()
                ->first();

            if (!$temporaryUpload) {
                return response()->json([
                    'success' => false,
                    'message' => 'Temporary upload not found or expired'
                ], 404);
            }

            // Create RajaGaleri instance for final media handling
            $rajaGaleri = new \Modules\Rajapicker\Models\RajaGaleri();
            $rajaGaleri->save();

            // Move media from temporary to final destination
            $finalMedia = $temporaryUpload->moveMedia($rajaGaleri, $request->collection);

            // Set user_id secara manual
            if (Auth::check()) {
                $finalMedia->update(['user_id' => Auth::id()]);
                $finalMedia->refresh();
            }

            // Generate thumbnail menggunakan service custom
            $this->generateThumbnailsForMedia($finalMedia);

            // Return final media data
            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $finalMedia->id,
                    'name' => $finalMedia->name,
                    'file_name' => $finalMedia->file_name,
                    'url' => $this->getRelativeUrl($finalMedia),
                    'thumbnail_url' => $this->thumbnailService->getThumbnailUrl($finalMedia->getRelativeUrlAttribute(), (new \Modules\Rajapicker\Services\RajaPickerConfigService())->getDefaultPreviewThumbnailSize()),
                    'mime_type' => $finalMedia->mime_type,
                    'size' => $finalMedia->size,
                    'collection_name' => $finalMedia->collection_name,
                    'user_id' => $finalMedia->user_id,
                    'created_at' => $finalMedia->created_at,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error moving temporary upload to final: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to move temporary upload to final destination'
            ], 500);
        }
    }

    /**
     * Check if uploaded file is an image
     */
    private function isImageFile($file): bool
    {
        $mimeType = $file->getMimeType();
        $supportedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp'];

        return in_array($mimeType, $supportedTypes);
    }

    /**
     * Generate relative URL without domain for media
     */
    private function getRelativeUrl($media): string
    {
        // Buat path berdasarkan CustomPathGenerator (collection_name/file_name)
        $path = $media->collection_name . '/' . $media->file_name;

        // Gunakan prefix dari config
        $prefix = config('media-library.prefix', 'uploads');

        // Return URL relatif tanpa domain
        return '/storage/' . $prefix . '/' . $path;
    }

    /**
     * Bangun nama file berdasarkan konfigurasi rajapicker.file_naming
     */
    private function generateFileName($file): string
    {
        $configService = new RajaPickerConfigService();
        $cfg = $configService->getFileNamingConfig();

        $extension = strtolower($file->getClientOriginalExtension());

        // Nama dasar
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $separator = $cfg['separator'] ?? '-';
        $nameSlug = \Illuminate\Support\Str::slug($originalName, $separator);

        if (!($cfg['lowercase'] ?? true)) {
            $nameSlug = \Illuminate\Support\Str::of($nameSlug)->upper();
        }

        // Token timestamp dan random
        $timestamp = $cfg['append_timestamp'] ? time() : '';
        $randomLen = (int) ($cfg['append_random_length'] ?? 0);
        $random = $randomLen > 0 ? Str::lower(Str::random($randomLen)) : '';

        // Build pattern
        $pattern = $cfg['pattern'] ?? '{name}{sep}{timestamp}{sep}{random}';

        $filenameWithoutExt = str_replace([
            '{name}',
            '{timestamp}',
            '{random}',
            '{sep}'
        ], [
            $nameSlug,
            $timestamp,
            $random,
            $separator
        ], $pattern);

        // Bersihkan kemungkinan separator ganda
        $filenameWithoutExt = preg_replace('/' . preg_quote($separator, '/') . '{2,}/', $separator, $filenameWithoutExt);
        $filenameWithoutExt = trim($filenameWithoutExt, $separator);

        return $filenameWithoutExt . '.' . $extension;
    }

    /**
     * Ubah URL absolut menjadi relatif (hilangkan protokol & domain)
     */
    private function toRelativeUrl(string $absoluteUrl): string
    {
        // Jika sudah relatif, kembalikan apa adanya
        if (str_starts_with($absoluteUrl, '/')) {
            return $absoluteUrl;
        }

        $parsed = parse_url($absoluteUrl);
        if (!$parsed || !isset($parsed['path'])) {
            return $absoluteUrl; // fallback
        }

        return $parsed['path'];
    }

    /**
     * Generate thumbnails untuk media menggunakan service custom
     */
    private function generateThumbnailsForMedia($media): void
    {
        try {
            // Service akan otomatis mengecek konfigurasi, tidak perlu dicek di sini

            // Cek apakah file adalah gambar
            if (!$media->isImage()) {
                return;
            }

            // Dapatkan path file relatif
            $filePath = $media->getRelativeUrlAttribute();
            
            if (!$filePath) {
                Log::warning('File path tidak ditemukan untuk media ID: ' . $media->id);
                return;
            }

            // Generate semua thumbnail
            $thumbnails = $this->thumbnailService->generateAllThumbnails($filePath);
            
            if (!empty($thumbnails)) {
                Log::info('Thumbnails berhasil dibuat untuk media ID: ' . $media->id, [
                    'file_path' => $filePath,
                    'thumbnails' => $thumbnails
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Error generating thumbnails for media ID ' . $media->id . ': ' . $e->getMessage());
        }
    }
}
