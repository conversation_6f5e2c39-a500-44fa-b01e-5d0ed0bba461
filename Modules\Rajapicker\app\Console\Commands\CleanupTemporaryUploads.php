<?php

namespace Modules\Rajapicker\Console\Commands;

use Illuminate\Console\Command;
use <PERSON><PERSON><PERSON>\Rajapicker\Models\TemporaryMediaUpload;

class CleanupTemporaryUploads extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rajapicker:cleanup-temp-uploads';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired temporary media uploads';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting cleanup of expired temporary uploads...');

        $count = TemporaryMediaUpload::cleanupExpired();

        $this->info("Cleaned up {$count} expired temporary uploads.");

        return Command::SUCCESS;
    }
}
