<?php

namespace Modules\Rajapicker\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Support\Str;

class TemporaryMediaUpload extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'session_id',
        'uuid',
        'name',
        'collection_name',
        'original_name',
        'file_size',
        'mime_type',
        'expires_at',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'file_size' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
            if (empty($model->session_id)) {
                $model->session_id = session()->getId();
            }
            if (empty($model->expires_at)) {
                $model->expires_at = now()->addHours(24); // Expire after 24 hours
            }
        });
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('default')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']);

        $this->addMediaCollection('gallery')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('cms')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('documents')
            ->acceptsMimeTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);

        $this->addMediaCollection('avatars')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
            ->singleFile();

        $this->addMediaCollection('banners')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('products')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
    }

    /**
     * Register media conversions
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(300)
            ->sharpen(10)
            ->nonQueued();

        $this->addMediaConversion('preview')
            ->width(500)
            ->height(500)
            ->sharpen(10)
            ->nonQueued();
    }

    /**
     * Move media from temporary upload to target model
     */
    public function moveMedia($targetModel, string $collectionName = 'default', string $diskName = '', string $fileName = ''): Media
    {
        $media = $this->getFirstMedia();
        
        if (!$media) {
            throw new \Exception('No media found in temporary upload');
        }

        // Get the file path
        $filePath = $media->getPath();
        
        // Create new media on target model
        $fileAdder = $targetModel->addMedia($filePath);
        
        if (!empty($fileName)) {
            $fileAdder->usingFileName($fileName);
        }
        
        if (!empty($media->name)) {
            $fileAdder->usingName($media->name);
        }

        // Move to target collection
        $newMedia = $fileAdder->toMediaCollection($collectionName, $diskName);

        // Copy custom properties
        if (!empty($media->custom_properties)) {
            $newMedia->custom_properties = $media->custom_properties;
            $newMedia->save();
        }

        // Delete temporary upload and its media
        $this->delete();

        return $newMedia;
    }

    /**
     * Scope for current session
     */
    public function scopeForCurrentSession($query)
    {
        return $query->where('session_id', session()->getId());
    }

    /**
     * Scope for expired uploads
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * Clean up expired temporary uploads
     */
    public static function cleanupExpired()
    {
        $expired = static::expired()->get();
        
        foreach ($expired as $upload) {
            $upload->delete();
        }
        
        return $expired->count();
    }
}
